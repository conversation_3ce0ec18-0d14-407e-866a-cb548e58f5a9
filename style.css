/*
Theme Name: mv-tv.com - Mobile Optimized
*/

/* ===== ENHANCED MASTER HEADER ===== */
.master-head {
    position: relative !important;
    background: #000000 !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
    border-bottom: 1px solid rgba(255, 107, 157, 0.3) !important;
    overflow: hidden !important;
}

/* Animated Graphic Background Effects */
.master-head::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 15% 25%, rgba(255, 107, 157, 0.15) 0%, transparent 40%),
        radial-gradient(circle at 85% 75%, rgba(116, 185, 255, 0.12) 0%, transparent 40%),
        radial-gradient(circle at 50% 10%, rgba(196, 69, 105, 0.08) 0%, transparent 30%),
        radial-gradient(circle at 20% 80%, rgba(108, 92, 231, 0.1) 0%, transparent 35%);
    animation: floatingGraphics 25s ease-in-out infinite;
    z-index: 1;
}

.master-head::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        linear-gradient(45deg, transparent 30%, rgba(255, 107, 157, 0.03) 32%, transparent 34%),
        linear-gradient(-45deg, transparent 30%, rgba(116, 185, 255, 0.03) 32%, transparent 34%),
        linear-gradient(90deg, transparent 48%, rgba(196, 69, 105, 0.02) 50%, transparent 52%);
    background-size: 60px 60px, 80px 80px, 100px 100px;
    animation: geometricPattern 30s linear infinite;
    z-index: 2;
}

/* ===== ENHANCED SITE BRANDING ===== */
.site-branding {
    position: relative !important;
    z-index: 10 !important;
    width: 100% !important;
}

.site-branding h1 {
    text-align: center !important;
    color: white !important;
    font-size: 16px !important;
    font-family: 'Kanit', sans-serif !important;
    font-weight: 600 !important;
    background: linear-gradient(45deg, #ff6b9d, #74b9ff, #ff6b9d) !important;
    background-size: 300% 300% !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    animation: titleGlow 4s ease-in-out infinite !important;
    text-shadow: 0 0 20px rgba(255, 107, 157, 0.3) !important;
    margin-bottom: 15px !important;
}

/* ===== ENHANCED HEADER INFO ===== */
.header-info {
    width: 1440px !important;
    margin: 0 auto !important;
    display: grid !important;
    grid-template-columns: 15% 70% 15% !important;
    align-items: center !important;
    padding: 10px 0 25px !important;
    position: relative !important;
    z-index: 10 !important;
}

/* ===== ENHANCED HEADER LOGO ===== */
section.header-logo {
    position: relative !important;
    z-index: 10 !important;
}

section.header-logo img {
    width: 220px !important;
    object-fit: cover !important;
    float: right !important;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    filter: drop-shadow(0 2px 8px rgba(255, 107, 157, 0.3)) !important;
    border-radius: 8px !important;
}

section.header-logo img:hover {
    transform: scale(1.05) rotate(1deg) !important;
    filter: drop-shadow(0 4px 12px rgba(255, 107, 157, 0.5)) !important;
}

/* ===== ENHANCED SEARCH INPUT ===== */
.search-input {
    position: relative !important;
    z-index: 10 !important;
}

#searchform {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    position: relative !important;
}

input#input-ser {
    padding: 8px 20px !important;
    border-radius: 20px !important;
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(10px) !important;
    color: white !important;
    border: 1px solid rgba(255, 107, 157, 0.3) !important;
    margin: 0 !important;
    background-image: none !important;
    text-indent: 0 !important;
    width: 75% !important;
    font-size: 14px !important;
    outline: 0 !important;
    font-family: 'Kanit', sans-serif !important;
    transition: all 0.3s ease !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2) !important;
}

input#input-ser::placeholder {
    color: rgba(255, 255, 255, 0.7) !important;
    font-weight: 300 !important;
}

input#input-ser:focus {
    background: rgba(255, 255, 255, 0.15) !important;
    border-color: rgba(116, 185, 255, 0.5) !important;
    box-shadow: 0 6px 20px rgba(116, 185, 255, 0.3) !important;
}

/* Search Icon */
#searchform::after {
    content: '🔍';
    position: absolute;
    right: 15%;
    top: 50%;
    transform: translateY(-50%);
    font-size: 16px;
    opacity: 0.8;
    animation: searchPulse 2s ease-in-out infinite;
    z-index: 11;
}

/* ===== ENHANCED CONTACT BUTTON ===== */
.hd-contact a {
    background: linear-gradient(148deg, #ff6b9d, #c44569, #74b9ff) !important;
    padding: 10px 25px !important;
    font-size: 16px !important;
    line-height: 1 !important;
    font-family: 'Kanit', sans-serif !important;
    font-weight: 500 !important;
    color: white !important;
    border-radius: 25px !important;
    display: inline-flex !important;
    align-items: center !important;
    width: 180px !important;
    height: 50px !important;
    justify-content: center !important;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3) !important;
    position: relative !important;
    overflow: hidden !important;
    z-index: 10 !important;
}

.hd-contact a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s;
}

.hd-contact a:hover::before {
    left: 100%;
}

.hd-contact a:hover {
    transform: translateY(-2px) scale(1.02) !important;
    box-shadow: 0 6px 20px rgba(116, 185, 255, 0.4) !important;
}

/* ===== ENHANCED MAIN NAVIGATION ===== */
.main-navigation {
    display: block !important;
    width: 100% !important;
    border-top: 1px solid rgba(255, 107, 157, 0.2) !important;
    background: rgba(0, 0, 0, 0.3) !important;
    backdrop-filter: blur(10px) !important;
    position: relative !important;
    z-index: 10 !important;
}

.main-navigation ul {
    display: block !important;
    list-style: none !important;
    margin: 0 !important;
    padding-left: 0 !important;
    text-align: center !important;
}

.menu-menu-container {
    width: 100% !important;
    text-align: center !important;
}

.main-navigation li {
    position: relative !important;
    display: inline-flex !important;
    padding: 0 1.5% !important;
}

.main-navigation a {
    display: block !important;
    text-decoration: none !important;
    padding: 15px 10px !important;
    font-family: 'Kanit', sans-serif !important;
    font-weight: 500 !important;
    color: rgba(255, 255, 255, 0.8) !important;
    position: relative !important;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    border-radius: 20px !important;
    margin: 3px !important;
    font-size: 14px !important;
}

.main-navigation a:before {
    content: "" !important;
    position: absolute !important;
    height: 3px !important;
    left: 20% !important;
    right: 20% !important;
    bottom: 5px !important;
    border-radius: 10px !important;
    background: linear-gradient(148deg, #ff6b9d, #74b9ff, #c44569) !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
}

.main-navigation a:hover,
.main-navigation .current-menu-item a {
    color: #ff6b9d !important;
    background: rgba(255, 107, 157, 0.1) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 15px rgba(255, 107, 157, 0.2) !important;
}

.main-navigation a:hover:before,
.main-navigation .current-menu-item a:before {
    visibility: visible !important;
}

/* ===== CONTAINER INDEX ===== */
.container-index {
    width: 1440px !important;
    margin: 0 auto !important;
    position: relative !important;
}

/* ===== ENHANCED MOVIE GRID ===== */
.movies-grid,
.movie-list,
.content-area,
.main-content,
.site-main {
    width: 100% !important;
    max-width: 1200px !important;
    margin: 0 auto !important;
    display: flex !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
    align-items: flex-start !important;
    gap: 20px !important;
    padding: 30px 20px !important;
    background: transparent !important;
}

/* Enhanced Movie Items */
.movie-item,
.post-item,
.film-item,
article {
    margin: 0 !important;
    width: 200px !important;
    max-width: 200px !important;
    background: rgba(255, 255, 255, 0.03) !important;
    border-radius: 12px !important;
    overflow: hidden !important;
    border: 1px solid rgba(255, 107, 157, 0.1) !important;
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    position: relative !important;
    backdrop-filter: blur(5px) !important;
}

.movie-item::before,
.post-item::before,
.film-item::before,
article::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 107, 157, 0.05), rgba(116, 185, 255, 0.05));
    opacity: 0;
    transition: opacity 0.4s ease;
    z-index: 1;
    border-radius: 12px;
}

/* Enhanced Movie Poster */
.movie-poster,
.post-thumbnail,
.film-poster {
    width: 200px !important;
    height: 280px !important;
    object-fit: cover !important;
    border-radius: 12px 12px 0 0 !important;
    overflow: hidden !important;
    position: relative !important;
    background: linear-gradient(135deg, #1a1a1a, #2d2d2d) !important;
}

.movie-poster img,
.post-thumbnail img,
.film-poster img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    filter: brightness(0.9) contrast(1.1) !important;
}

/* Enhanced Hover Effects */
.movie-item:hover,
.post-item:hover,
.film-item:hover,
article:hover {
    transform: translateY(-10px) scale(1.03) !important;
    box-shadow:
        0 20px 60px rgba(255, 107, 157, 0.25),
        0 10px 30px rgba(116, 185, 255, 0.15) !important;
    border-color: rgba(255, 107, 157, 0.4) !important;
    z-index: 10 !important;
}

.movie-item:hover::before,
.post-item:hover::before,
.film-item:hover::before,
article:hover::before {
    opacity: 1;
}

.movie-item:hover .movie-poster img,
.post-item:hover .post-thumbnail img,
.film-item:hover .film-poster img {
    transform: scale(1.15) rotate(1deg) !important;
    filter: brightness(1.2) contrast(1.3) saturate(1.1) !important;
}

/* Enhanced Movie Info */
.movie-info,
.post-info,
.film-info {
    padding: 15px !important;
    text-align: center !important;
    position: relative !important;
    z-index: 2 !important;
    background: rgba(0, 0, 0, 0.7) !important;
    backdrop-filter: blur(10px) !important;
}

.movie-title,
.post-title,
.film-title {
    color: rgba(255, 255, 255, 0.9) !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    margin-bottom: 8px !important;
    line-height: 1.3 !important;
    transition: all 0.3s ease !important;
    font-family: 'Kanit', sans-serif !important;
}

.movie-item:hover .movie-title,
.post-item:hover .post-title,
.film-item:hover .film-title {
    color: #ff6b9d !important;
    text-shadow: 0 0 10px rgba(255, 107, 157, 0.5) !important;
}

/* Enhanced Quality Tags */
.quality-tag,
.movie-quality,
.post-quality {
    position: absolute !important;
    top: 10px !important;
    left: 10px !important;
    background: linear-gradient(135deg, rgba(255, 107, 157, 0.9), rgba(196, 69, 105, 0.9)) !important;
    color: white !important;
    padding: 6px 10px !important;
    border-radius: 8px !important;
    font-size: 11px !important;
    font-weight: 700 !important;
    z-index: 5 !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5) !important;
}

/* Enhanced Rating */
.movie-rating,
.post-rating,
.film-rating {
    background: linear-gradient(135deg, #ff6b9d, #74b9ff) !important;
    color: white !important;
    padding: 4px 8px !important;
    border-radius: 10px !important;
    font-size: 11px !important;
    font-weight: 700 !important;
    display: inline-block !important;
    margin-top: 5px !important;
    box-shadow: 0 2px 8px rgba(255, 107, 157, 0.3) !important;
}

/* Section Titles */
.section-title,
h2.title,
.content-title {
    text-align: center !important;
    width: 100% !important;
    margin: 30px 0 !important;
    color: white !important;
    font-family: 'Kanit', sans-serif !important;
    font-size: 24px !important;
    font-weight: 700 !important;
    background: linear-gradient(135deg, #ff6b9d, #74b9ff, #c44569) !important;
    background-size: 300% 300% !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    animation: titleGlow 4s ease-in-out infinite !important;
}

/* ===== ANIMATIONS ===== */
@keyframes floatingGraphics {
    0%, 100% {
        opacity: 1;
        transform: translateY(0px) scale(1);
    }
    25% {
        opacity: 0.8;
        transform: translateY(-15px) scale(1.05);
    }
    50% {
        opacity: 0.6;
        transform: translateY(-8px) scale(0.95);
    }
    75% {
        opacity: 0.9;
        transform: translateY(-20px) scale(1.02);
    }
}

@keyframes geometricPattern {
    0% {
        background-position: 0px 0px, 0px 0px, 0px 0px;
    }
    100% {
        background-position: 60px 60px, -80px 80px, 100px -100px;
    }
}

@keyframes titleGlow {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

@keyframes searchPulse {
    0%, 100% { opacity: 0.8; transform: translateY(-50%) scale(1); }
    50% { opacity: 1; transform: translateY(-50%) scale(1.1); }
}

/* ===== RESPONSIVE DESIGN - ปรับปรุงสำหรับมือถือ ===== */
@media screen and (max-width: 1440px) {
    .header-info {
        width: 100% !important;
        max-width: 1440px !important;
        margin: 0 auto !important;
        display: grid !important;
        grid-template-columns: 15% 70% 15% !important;
        align-items: center !important;
        padding: 10px 20px 25px !important;
    }

    .container-index {
        width: 100% !important;
        max-width: 1440px !important;
        margin: 0 auto !important;
        padding: 0 20px !important;
    }

    .movies-grid,
    .movie-list,
    .content-area,
    .main-content,
    .site-main {
        max-width: 100% !important;
        margin: 0 auto !important;
        padding: 30px 20px !important;
    }
}

@media screen and (max-width: 1200px) {
    .header-info {
        grid-template-columns: 20% 60% 20% !important;
        padding: 15px !important;
    }

    section.header-logo img {
        width: 180px !important;
    }

    .movies-grid,
    .movie-list,
    .content-area,
    .main-content,
    .site-main {
        display: grid !important;
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)) !important;
        gap: 18px !important;
        justify-items: center !important;
    }

    .movie-item,
    .post-item,
    .film-item,
    article {
        width: 180px !important;
        max-width: 180px !important;
    }

    .movie-poster,
    .post-thumbnail,
    .film-poster {
        width: 180px !important;
        height: 250px !important;
    }
}

/* ===== MOBILE DESIGN - ออกแบบใหม่สำหรับมือถือ ===== */
@media screen and (max-width: 768px) {
    /* Header สำหรับมือถือ */
    .header-info {
        width: 100% !important;
        display: grid !important;
        grid-template-columns: 1fr !important;
        grid-template-rows: auto auto auto !important;
        gap: 15px !important;
        text-align: center !important;
        padding: 15px !important;
    }

    /* Logo สำหรับมือถือ */
    section.header-logo {
        order: 1 !important;
        text-align: center !important;
    }

    section.header-logo img {
        width: 150px !important;
        float: none !important;
        margin: 0 auto !important;
        display: block !important;
    }

    /* Search สำหรับมือถือ */
    .search-input {
        order: 2 !important;
        width: 100% !important;
    }

    input#input-ser {
        width: 90% !important;
        padding: 12px 20px !important;
        font-size: 16px !important;
        border-radius: 25px !important;
    }

    #searchform::after {
        right: 8% !important;
        font-size: 18px !important;
    }

    /* Contact Button สำหรับมือถือ */
    .hd-contact {
        order: 3 !important;
        text-align: center !important;
    }

    .hd-contact a {
        width: 200px !important;
        height: 50px !important;
        font-size: 16px !important;
        margin: 0 auto !important;
    }

    /* Navigation สำหรับมือถือ */
    .main-navigation {
        background: rgba(0, 0, 0, 0.5) !important;
        padding: 10px 0 !important;
    }

    .main-navigation li {
        display: block !important;
        padding: 5px 0 !important;
        width: 100% !important;
    }

    .main-navigation a {
        display: block !important;
        width: 90% !important;
        margin: 5px auto !important;
        padding: 12px 20px !important;
        text-align: center !important;
        font-size: 16px !important;
        border-radius: 25px !important;
    }

    /* Site Branding สำหรับมือถือ */
    .site-branding h1 {
        font-size: 20px !important;
        margin-bottom: 20px !important;
    }

    /* Movie Grid สำหรับมือถือ */
    .movies-grid,
    .movie-list,
    .content-area,
    .main-content,
    .site-main {
        display: grid !important;
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 15px !important;
        padding: 20px 15px !important;
        justify-items: center !important;
    }

    .movie-item,
    .post-item,
    .film-item,
    article {
        width: 160px !important;
        max-width: 160px !important;
    }

    .movie-poster,
    .post-thumbnail,
    .film-poster {
        width: 160px !important;
        height: 220px !important;
    }

    .movie-title,
    .post-title,
    .film-title {
        font-size: 14px !important;
        line-height: 1.4 !important;
    }

    /* Section Titles สำหรับมือถือ */
    .section-title,
    h2.title,
    .content-title {
        font-size: 20px !important;
        margin: 20px 0 !important;
        padding: 0 15px !important;
    }

    /* ปรับปรุง Hover Effects สำหรับมือถือ */
    .movie-item:hover,
    .post-item:hover,
    .film-item:hover,
    article:hover {
        transform: translateY(-5px) scale(1.02) !important;
        box-shadow:
            0 10px 30px rgba(255, 107, 157, 0.2),
            0 5px 15px rgba(116, 185, 255, 0.1) !important;
    }
}

@media screen and (max-width: 480px) {
    /* Header สำหรับหน้าจอเล็ก */
    .header-info {
        padding: 10px !important;
        gap: 10px !important;
    }

    section.header-logo img {
        width: 120px !important;
    }

    input#input-ser {
        width: 85% !important;
        padding: 10px 15px !important;
        font-size: 14px !important;
    }

    .hd-contact a {
        width: 160px !important;
        height: 45px !important;
        font-size: 14px !important;
    }

    .site-branding h1 {
        font-size: 18px !important;
    }

    /* Navigation สำหรับหน้าจอเล็ก */
    .main-navigation a {
        padding: 10px 15px !important;
        font-size: 14px !important;
    }

    /* Movie Grid สำหรับหน้าจอเล็ก */
    .movies-grid,
    .movie-list,
    .content-area,
    .main-content,
    .site-main {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 12px !important;
        padding: 15px 10px !important;
    }

    .movie-item,
    .post-item,
    .film-item,
    article {
        width: 140px !important;
        max-width: 140px !important;
    }

    .movie-poster,
    .post-thumbnail,
    .film-poster {
        width: 140px !important;
        height: 190px !important;
    }

    .movie-title,
    .post-title,
    .film-title {
        font-size: 13px !important;
    }

    .movie-info,
    .post-info,
    .film-info {
        padding: 10px !important;
    }

    /* Section Titles สำหรับหน้าจอเล็ก */
    .section-title,
    h2.title,
    .content-title {
        font-size: 18px !important;
        margin: 15px 0 !important;
    }

    /* Quality Tags สำหรับหน้าจอเล็ก */
    .quality-tag,
    .movie-quality,
    .post-quality {
        padding: 4px 8px !important;
        font-size: 10px !important;
        top: 8px !important;
        left: 8px !important;
    }

    /* Rating สำหรับหน้าจอเล็ก */
    .movie-rating,
    .post-rating,
    .film-rating {
        padding: 3px 6px !important;
        font-size: 10px !important;
    }
}
